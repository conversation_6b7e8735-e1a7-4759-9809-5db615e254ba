# Database Setup Guide

## PostgreSQL Installation

### Ubuntu/Debian
```bash
# Update package list
sudo apt update

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Check status
sudo systemctl status postgresql
```

### macOS (using Homebrew)
```bash
# Install PostgreSQL
brew install postgresql

# Start PostgreSQL service
brew services start postgresql

# Check status
brew services list | grep postgresql
```

### Windows
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Run the installer
3. Follow the setup wizard
4. Remember the password you set for the postgres user

## Database Setup

### 1. Connect to PostgreSQL
```bash
# Switch to postgres user (Linux/macOS)
sudo -u postgres psql

# Or connect directly (Windows)
psql -U postgres
```

### 2. Create Database and User
```sql
-- Create database
CREATE DATABASE lux_voyage;

-- Create user
CREATE USER lux_voyage_user WITH ENCRYPTED PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE lux_voyage TO lux_voyage_user;

-- Connect to the database
\c lux_voyage

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO lux_voyage_user;

-- Exit PostgreSQL
\q
```

### 3. Update Environment Variables
Create or update your `.env` file:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=lux_voyage
DB_USER=lux_voyage_user
DB_PASSWORD=your_secure_password
DB_SSL=false
```

## Running Migrations

### 1. Install Dependencies
```bash
npm install
```

### 2. Run Database Migration
```bash
npm run db:migrate
```

### 3. Verify Setup
```bash
npm run test:env
```

## Troubleshooting

### Connection Issues
1. **Check PostgreSQL is running:**
   ```bash
   sudo systemctl status postgresql
   ```

2. **Check connection:**
   ```bash
   psql -h localhost -U lux_voyage_user -d lux_voyage
   ```

3. **Reset password if needed:**
   ```sql
   ALTER USER lux_voyage_user WITH PASSWORD 'new_password';
   ```

### Permission Issues
1. **Check PostgreSQL configuration:**
   ```bash
   sudo nano /etc/postgresql/*/main/pg_hba.conf
   ```

2. **Add local connection:**
   ```
   local   all             all                                     md5
   host    all             all             127.0.0.1/32            md5
   host    all             all             ::1/128                 md5
   ```

3. **Restart PostgreSQL:**
   ```bash
   sudo systemctl restart postgresql
   ```

### Database Creation Issues
1. **Check if database exists:**
   ```sql
   \l
   ```

2. **Drop and recreate if needed:**
   ```sql
   DROP DATABASE IF EXISTS lux_voyage;
   CREATE DATABASE lux_voyage;
   ```

## Verification

After setup, run these commands to verify everything is working:

```bash
# Test environment
npm run test:env

# Test database connection
npm run health

# Run API tests
npm run test:api
```

## Production Considerations

1. **Use strong passwords**
2. **Enable SSL in production**
3. **Set up regular backups**
4. **Configure connection pooling**
5. **Monitor database performance**

## Backup and Restore

### Backup
```bash
pg_dump -h localhost -U lux_voyage_user lux_voyage > backup.sql
```

### Restore
```bash
psql -h localhost -U lux_voyage_user lux_voyage < backup.sql
``` 