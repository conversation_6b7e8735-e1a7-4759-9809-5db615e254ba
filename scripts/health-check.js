const axios = require('axios');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

class HealthChecker {
  constructor() {
    this.results = {
      server: false,
      database: false,
      fileSystem: false,
      api: false,
      uploads: false
    };
    
    // Get API URL from environment
    this.apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
    
    // Database configuration
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'lux_voyage',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD,
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
    };
  }

  async checkServer() {
    try {
      const response = await axios.get(`${this.apiUrl}/api/health`, {
        timeout: 5000
      });
      this.results.server = response.status === 200;
      console.log('✓ Server is running');
    } catch (error) {
      console.log('✗ Server is not responding');
    }
  }

  async checkDatabase() {
    try {
      const pool = new Pool(this.dbConfig);
      const result = await pool.query('SELECT NOW()');
      await pool.end();
      this.results.database = true;
      console.log('✓ Database connection successful');
    } catch (error) {
      console.log('✗ Database connection failed');
    }
  }

  checkFileSystem() {
    try {
      const requiredDirs = ['uploads', 'logs', 'build'];
      const allExist = requiredDirs.every(dir => {
        const exists = fs.existsSync(dir);
        if (!exists) {
          console.log(`✗ Directory missing: ${dir}`);
        }
        return exists;
      });
      
      if (allExist) {
        this.results.fileSystem = true;
        console.log('✓ File system check passed');
      }
    } catch (error) {
      console.log('✗ File system check failed');
    }
  }

  async checkAPI() {
    try {
      const endpoints = [
        '/api/auth/login',
        '/api/destinations',
        '/api/experiences',
        '/api/offers',
        '/api/inquiries'
      ];

      const checks = endpoints.map(async (endpoint) => {
        try {
          await axios.get(`${this.apiUrl}${endpoint}`, {
            timeout: 3000
          });
          return true;
        } catch (error) {
          return false;
        }
      });

      const results = await Promise.all(checks);
      this.results.api = results.every(result => result);
      
      if (this.results.api) {
        console.log('✓ API endpoints responding');
      } else {
        console.log('✗ Some API endpoints not responding');
      }
    } catch (error) {
      console.log('✗ API check failed');
    }
  }

  checkUploads() {
    try {
      const uploadsDir = path.join(__dirname, '..', 'uploads');
      const stats = fs.statSync(uploadsDir);
      this.results.uploads = stats.isDirectory();
      
      if (this.results.uploads) {
        console.log('✓ Uploads directory accessible');
      } else {
        console.log('✗ Uploads directory not accessible');
      }
    } catch (error) {
      console.log('✗ Uploads check failed');
    }
  }

  async runAllChecks() {
    console.log('Starting health check...\n');
    
    await this.checkServer();
    await this.checkDatabase();
    this.checkFileSystem();
    await this.checkAPI();
    this.checkUploads();

    console.log('\nHealth Check Summary:');
    console.log('====================');
    
    Object.entries(this.results).forEach(([check, status]) => {
      const icon = status ? '✓' : '✗';
      const statusText = status ? 'PASS' : 'FAIL';
      console.log(`${icon} ${check.toUpperCase()}: ${statusText}`);
    });

    const allPassed = Object.values(this.results).every(result => result);
    
    if (allPassed) {
      console.log('\n🎉 All health checks passed!');
      process.exit(0);
    } else {
      console.log('\n❌ Some health checks failed!');
      process.exit(1);
    }
  }
}

// Run health check
const healthChecker = new HealthChecker();
healthChecker.runAllChecks(); 