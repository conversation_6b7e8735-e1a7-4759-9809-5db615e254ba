const path = require('path');
// Attempt to get the existing database connection
let db;
try {
    console.log("Attempting to connect to database...");
    // Assuming init.js is in ../database/ from scripts/
    // The init.js exports { initializeDatabase, db }
    // We need the db object directly.
    const dbModule = require(path.join(__dirname, '..', 'database', 'init.js'));
    db = dbModule.db; // Access the exported db object

    if (db && db.open) { // Check if db object is a valid better-sqlite3 instance
        console.log("Database connected successfully.");
    } else {
        // This case should ideally not be hit if require itself doesn't throw
        // and if init.js always exports a valid db object or throws.
        console.error("Failed to connect to database: Database object is invalid or not initialized.");
        process.exit(1); // Exit if DB connection is not valid
    }
} catch (error) {
    console.error("Failed to connect to database:", error.message);
    process.exit(1); // Exit if there's an error during connection
}

async function verifyDatabaseWrite() {
    const testMessage = 'Test write from Kilo Code';
    const testTimestamp = new Date().toISOString();
    let insertedRowId;

    try {
        // 1. Create test table
        console.log("Attempting to create test table...");
        try {
            const createTableStmt = db.prepare(`
                CREATE TABLE IF NOT EXISTS kilo_code_test_write (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    message TEXT,
                    timestamp TEXT
                );
            `);
            createTableStmt.run();
            console.log("Test table created/ensured successfully.");
        } catch (error) {
            console.error("Failed to create test table:", error.message);
            throw error; // Propagate error to main catch block for cleanup
        }

        // 2. Insert test row
        console.log("Attempting to insert test row...");
        try {
            const insertStmt = db.prepare(`
                INSERT INTO kilo_code_test_write (message, timestamp)
                VALUES (?, ?);
            `);
            const info = insertStmt.run(testMessage, testTimestamp);
            insertedRowId = info.lastInsertRowid;
            console.log(`Test row inserted successfully. Row ID: ${insertedRowId}`);
        } catch (error) {
            console.error("Failed to insert test row:", error.message);
            throw error;
        }

        // 3. Select test row
        console.log("Attempting to select test row...");
        try {
            const selectStmt = db.prepare(`
                SELECT * FROM kilo_code_test_write
                WHERE message = ? AND id = ?;
            `);
            const selectedRow = selectStmt.get(testMessage, insertedRowId);

            if (selectedRow) {
                console.log("Test row selected successfully:", JSON.stringify(selectedRow));
            } else {
                console.error("Test row not found after insert.");
            }
        } catch (error) {
            console.error("Failed to select test row:", error.message);
            throw error;
        }

    } catch (error) {
        // Error already logged in individual steps, or will be logged if from connection
        console.error("Verification script encountered an error:", error.message);
    } finally {
        if (db && db.open) {
            try {
                db.close();
                console.log("Database connection closed.");
            } catch (closeError) {
                console.error("Failed to close database connection:", closeError.message);
            }
        }
    }
}

verifyDatabaseWrite();