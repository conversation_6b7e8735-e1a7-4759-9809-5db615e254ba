const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🚀 Starting Lux Voyage Application...\n');

// Check if .env file exists
if (!fs.existsSync('.env')) {
  console.error('❌ .env file not found! Please create one based on .env.example');
  process.exit(1);
}

// Create required directories
const requiredDirs = ['uploads', 'logs', 'backups'];
requiredDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  }
});

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
if (majorVersion < 14) {
  console.error('❌ Node.js version 14 or higher is required');
  process.exit(1);
}
console.log(`✅ Node.js version: ${nodeVersion}`);

// Install dependencies if node_modules doesn't exist
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installing dependencies...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (error) {
    console.error('❌ Failed to install dependencies');
    process.exit(1);
  }
}

// Run database migration
console.log('🗄️  Setting up database...');
try {
  execSync('npm run db:migrate', { stdio: 'inherit' });
  console.log('✅ Database setup complete');
} catch (error) {
  console.error('❌ Database setup failed');
  process.exit(1);
}

// Run health check
console.log('🏥 Running health check...');
try {
  execSync('npm run health', { stdio: 'inherit' });
  console.log('✅ Health check passed');
} catch (error) {
  console.error('❌ Health check failed');
  process.exit(1);
}

console.log('\n🎉 Application is ready to start!');
console.log('\nAvailable commands:');
console.log('  npm run dev          - Start development server');
console.log('  npm run build:prod   - Build for production');
console.log('  npm run deploy:prod  - Deploy to production');
console.log('  npm run health       - Run health check');
console.log('  npm run test:api     - Test API integration'); 