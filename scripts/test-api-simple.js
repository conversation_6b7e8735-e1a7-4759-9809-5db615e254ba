const fetch = require('node-fetch');
const FormData = require('form-data');

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';
const TEST_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

// Test data
const testDestination = {
  title: 'Test Destination',
  slug: 'test-destination',
  description: 'A test destination for API testing',
  content: 'This is a detailed content for the test destination. It needs to be at least 50 characters long.',
  category: 'Test Category',
  featured: 0, // Use 0 instead of 'false'
  image_url: 'https://via.placeholder.com/800x600.png?text=Test+Destination'
};

const testOffer = {
  title: 'Test Special Offer',
  slug: 'test-special-offer',
  description: 'A test special offer for API testing',
  content: 'This is a detailed content for the test offer. It needs to be at least 50 characters long.',
  discount: 15,
  valid_from: new Date().toISOString().split('T')[0], // Today
  valid_to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
  featured: 0, // Use 0 instead of 'false'
  image_url: 'https://via.placeholder.com/800x600.png?text=Test+Offer'
};

// Global variables
let authToken;
let createdDestinationId;
let createdOfferId;

// Helper functions
async function login() {
  console.log('\n🔐 Authenticating...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_CREDENTIALS)
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Authentication failed: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Authentication successful');
    return data.token;
  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    process.exit(1);
  }
}

async function testDestinationCRUD() {
  console.log('\n🏝️ Testing Destination CRUD operations...');
  
  // CREATE
  console.log('\n📝 Creating a new destination...');
  try {
    // Add image_url to the validation schema
    const formData = new FormData();
    Object.entries(testDestination).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const response = await fetch(`${API_BASE_URL}/destinations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to create destination: ${data.message || response.statusText}`);
    }
    
    createdDestinationId = data.id;
    console.log(`✅ Destination created successfully with ID: ${createdDestinationId}`);
    console.log('Created destination data:', data);
  } catch (error) {
    console.error('❌ Create destination error:', error.message);
    // Continue with the test even if creation fails
  }
  
  // READ
  console.log('\n📖 Reading the created destination...');
  try {
    if (!createdDestinationId) {
      throw new Error('No destination ID available, skipping read test');
    }
    
    const response = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to read destination: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Destination read successfully');
    console.log('Retrieved destination data:', data);
    
    // Verify data integrity
    if (data.title !== testDestination.title || data.slug !== testDestination.slug) {
      console.warn('⚠️ Data integrity issue: Retrieved data does not match created data');
    }
  } catch (error) {
    console.error('❌ Read destination error:', error.message);
  }
  
  // UPDATE
  console.log('\n🔄 Updating the destination...');
  try {
    if (!createdDestinationId) {
      throw new Error('No destination ID available, skipping update test');
    }
    
    const updatedData = {
      ...testDestination,
      title: 'Updated Test Destination',
      description: 'This description has been updated for testing'
    };
    
    // Add image_url to the validation schema
    const formData = new FormData();
    Object.entries(updatedData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const response = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to update destination: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Destination updated successfully');
    console.log('Updated destination data:', data);
    
    // Verify update was successful
    if (data.title !== 'Updated Test Destination') {
      console.warn('⚠️ Update verification failed: Title was not updated correctly');
    }
  } catch (error) {
    console.error('❌ Update destination error:', error.message);
  }
  
  // DELETE
  console.log('\n🗑️ Deleting the destination...');
  try {
    if (!createdDestinationId) {
      throw new Error('No destination ID available, skipping delete test');
    }
    
    const response = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to delete destination: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Destination deleted successfully');
    console.log('Delete response:', data);
    
    // Verify deletion
    try {
      const verifyResponse = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`);
      const verifyData = await verifyResponse.json();
      
      if (verifyResponse.ok) {
        console.warn('⚠️ Deletion verification failed: Destination still exists');
      } else {
        console.log('✅ Deletion verified: Destination no longer exists');
      }
    } catch (error) {
      console.log('✅ Deletion verified: Destination no longer exists');
    }
  } catch (error) {
    console.error('❌ Delete destination error:', error.message);
  }
}

async function testOfferCRUD() {
  console.log('\n🏷️ Testing Offer CRUD operations...');
  
  // CREATE
  console.log('\n📝 Creating a new offer...');
  try {
    // Add image_url to the validation schema
    const formData = new FormData();
    Object.entries(testOffer).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const response = await fetch(`${API_BASE_URL}/offers`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to create offer: ${data.message || response.statusText}`);
    }
    
    createdOfferId = data.id;
    console.log(`✅ Offer created successfully with ID: ${createdOfferId}`);
    console.log('Created offer data:', data);
  } catch (error) {
    console.error('❌ Create offer error:', error.message);
    // Continue with the test even if creation fails
  }
  
  // READ
  console.log('\n📖 Reading the created offer...');
  try {
    if (!createdOfferId) {
      throw new Error('No offer ID available, skipping read test');
    }
    
    const response = await fetch(`${API_BASE_URL}/offers/${createdOfferId}`);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to read offer: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Offer read successfully');
    console.log('Retrieved offer data:', data);
    
    // Verify data integrity
    if (data.title !== testOffer.title || data.slug !== testOffer.slug) {
      console.warn('⚠️ Data integrity issue: Retrieved data does not match created data');
    }
  } catch (error) {
    console.error('❌ Read offer error:', error.message);
  }
  
  // UPDATE
  console.log('\n🔄 Updating the offer...');
  try {
    if (!createdOfferId) {
      throw new Error('No offer ID available, skipping update test');
    }
    
    const updatedData = {
      ...testOffer,
      title: 'Updated Test Offer',
      discount: 20
    };
    
    // Add image_url to the validation schema
    const formData = new FormData();
    Object.entries(updatedData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const response = await fetch(`${API_BASE_URL}/offers/${createdOfferId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to update offer: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Offer updated successfully');
    console.log('Updated offer data:', data);
    
    // Verify update was successful
    if (data.title !== 'Updated Test Offer' || data.discount !== 20) {
      console.warn('⚠️ Update verification failed: Title or discount was not updated correctly');
    }
  } catch (error) {
    console.error('❌ Update offer error:', error.message);
  }
  
  // DELETE
  console.log('\n🗑️ Deleting the offer...');
  try {
    if (!createdOfferId) {
      throw new Error('No offer ID available, skipping delete test');
    }
    
    const response = await fetch(`${API_BASE_URL}/offers/${createdOfferId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to delete offer: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Offer deleted successfully');
    console.log('Delete response:', data);
    
    // Verify deletion
    try {
      const verifyResponse = await fetch(`${API_BASE_URL}/offers/${createdOfferId}`);
      const verifyData = await verifyResponse.json();
      
      if (verifyResponse.ok) {
        console.warn('⚠️ Deletion verification failed: Offer still exists');
      } else {
        console.log('✅ Deletion verified: Offer no longer exists');
      }
    } catch (error) {
      console.log('✅ Deletion verified: Offer no longer exists');
    }
  } catch (error) {
    console.error('❌ Delete offer error:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🧪 Starting database update tests (simple version)...');
  
  try {
    // Step 1: Login to get auth token
    authToken = await login();
    
    // Step 2: Test Destination CRUD
    await testDestinationCRUD();
    
    // Step 3: Test Offer CRUD
    await testOfferCRUD();
    
    console.log('\n✅ All tests completed!');
  } catch (error) {
    console.error('\n❌ Test execution error:', error.message);
  }
}

// Run the tests
runTests();