const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';
const TEST_IMAGE_PATH = path.join(__dirname, '..', 'test-image.jpg');
const TEST_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

// Test data
const testDestination = {
  title: 'Test Destination',
  slug: 'test-destination-' + Date.now(), // Ensure unique slug
  description: 'A test destination for API testing',
  content: 'This is a detailed content for the test destination. It needs to be at least 50 characters long.',
  category: 'Test Category',
  featured: 0
};

// Global variables
let authToken;
let createdDestinationId;

// Helper functions
async function login() {
  console.log('\n🔐 Authenticating...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_CREDENTIALS)
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Authentication failed: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Authentication successful');
    return data.token;
  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    process.exit(1);
  }
}

async function testDestinationCRUD() {
  console.log('\n🏝️ Testing Destination CRUD operations...');
  
  // CREATE
  console.log('\n📝 Creating a new destination...');
  try {
    // Check if test image exists
    if (!fs.existsSync(TEST_IMAGE_PATH)) {
      console.error('❌ Test image not found at path:', TEST_IMAGE_PATH);
      throw new Error('Test image not found');
    }
    
    // Create form data with all required fields
    const formData = new FormData();
    
    // Add all destination fields
    Object.entries(testDestination).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    // Add image file
    const fileStream = fs.createReadStream(TEST_IMAGE_PATH);
    formData.append('image', fileStream, {
      filename: 'test-image.jpg',
      contentType: 'image/jpeg'
    });
    
    console.log('📎 Added test image to request');
    
    // Make the API request
    const response = await fetch(`${API_BASE_URL}/destinations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to create destination: ${JSON.stringify(data)}`);
    }
    
    createdDestinationId = data.id;
    console.log(`✅ Destination created successfully with ID: ${createdDestinationId}`);
    console.log('Created destination data:', data);
  } catch (error) {
    console.error('❌ Create destination error:', error.message);
    // Continue with the test even if creation fails
  }
  
  // READ
  console.log('\n📖 Reading the created destination...');
  try {
    if (!createdDestinationId) {
      throw new Error('No destination ID available, skipping read test');
    }
    
    const response = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to read destination: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Destination read successfully');
    console.log('Retrieved destination data:', data);
    
    // Verify data integrity
    if (data.title !== testDestination.title || data.slug !== testDestination.slug) {
      console.warn('⚠️ Data integrity issue: Retrieved data does not match created data');
    }
  } catch (error) {
    console.error('❌ Read destination error:', error.message);
  }
  
  // UPDATE
  console.log('\n🔄 Updating the destination...');
  try {
    if (!createdDestinationId) {
      throw new Error('No destination ID available, skipping update test');
    }
    
    const updatedData = {
      ...testDestination,
      title: 'Updated Test Destination',
      description: 'This description has been updated for testing'
    };
    
    // Create form data with all required fields
    const formData = new FormData();
    
    // Add all destination fields
    Object.entries(updatedData).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    // Make the API request
    const response = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to update destination: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Destination updated successfully');
    console.log('Updated destination data:', data);
    
    // Verify update was successful
    if (data.title !== 'Updated Test Destination') {
      console.warn('⚠️ Update verification failed: Title was not updated correctly');
    }
  } catch (error) {
    console.error('❌ Update destination error:', error.message);
  }
  
  // DELETE
  console.log('\n🗑️ Deleting the destination...');
  try {
    if (!createdDestinationId) {
      throw new Error('No destination ID available, skipping delete test');
    }
    
    const response = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to delete destination: ${data.message || response.statusText}`);
    }
    
    console.log('✅ Destination deleted successfully');
    console.log('Delete response:', data);
    
    // Verify deletion
    try {
      const verifyResponse = await fetch(`${API_BASE_URL}/destinations/${createdDestinationId}`);
      const verifyData = await verifyResponse.json();
      
      if (verifyResponse.ok) {
        console.warn('⚠️ Deletion verification failed: Destination still exists');
      } else {
        console.log('✅ Deletion verified: Destination no longer exists');
      }
    } catch (error) {
      console.log('✅ Deletion verified: Destination no longer exists');
    }
  } catch (error) {
    console.error('❌ Delete destination error:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🧪 Starting destination API tests...');
  
  try {
    // Step 1: Login to get auth token
    authToken = await login();
    
    // Step 2: Test Destination CRUD
    await testDestinationCRUD();
    
    console.log('\n✅ All tests completed!');
  } catch (error) {
    console.error('\n❌ Test execution error:', error.message);
  }
}

// Run the tests
runTests();