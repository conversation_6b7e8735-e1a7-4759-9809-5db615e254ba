const express = require('express');
const router = express.Router();
const { Experience } = require('../database/models');
const { 
  authenticateToken, 
  requireAdmin, 
  validateBody, 
  NotFoundError,
  ValidationError,
  uploadSingleFile,
  processUploadedFile
} = require('../middleware');

/**
 * @route GET /api/experiences
 * @desc Get all experiences
 * @access Public
 */
router.get('/', (req, res, next) => {
  try {
    const experiences = Experience.getAll();
    res.json(experiences);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/experiences/featured
 * @desc Get featured experiences
 * @access Public
 */
router.get('/featured', (req, res, next) => {
  try {
    const featuredExperiences = Experience.getFeatured();
    res.json(featuredExperiences);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/experiences/category/:category
 * @desc Get experiences by category
 * @access Public
 */
router.get('/category/:category', (req, res, next) => {
  try {
    const { category } = req.params;
    const experiences = Experience.getByCategory(category);
    res.json(experiences);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/experiences/:id
 * @desc Get experience by ID
 * @access Public
 */
router.get('/:id', (req, res, next) => {
  try {
    const experience = Experience.getById(parseInt(req.params.id));

    if (!experience) {
      throw new NotFoundError('Experience not found');
    }

    res.json(experience);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/experiences/slug/:slug
 * @desc Get experience by slug
 * @access Public
 */
router.get('/slug/:slug', (req, res, next) => {
  try {
    const experience = Experience.getBySlug(req.params.slug);

    if (!experience) {
      throw new NotFoundError('Experience not found');
    }

    res.json(experience);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/experiences/:id/edit
 * @desc Get experience data for editing
 * @access Private
 */
router.get('/:id/edit', authenticateToken, (req, res, next) => {
  try {
    const experience = Experience.getById(parseInt(req.params.id));

    if (!experience) {
      throw new NotFoundError('Experience not found');
    }

    // Get available categories for dropdown options
    const allExperiences = Experience.getAll();
    const categories = [...new Set(allExperiences.map(e => e.category))];

    // Get duration options
    const durationOptions = ['1 hour', '2 hours', '3 hours', '4 hours', 'Half day', 'Full day'];

    // Return the experience with additional metadata for editing
    res.json({
      item: experience,
      metadata: {
        categories,
        durationOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: [
          'title', 'slug', 'description', 'content', 'image_url', 'category',
          'duration', 'price', 'featured'
        ]
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Schema for experience validation
 */
const experienceSchema = {
  title: { required: true, type: 'string', min: 3, max: 100 },
  slug: { 
    required: true, 
    type: 'string', 
    min: 3, 
    max: 100,
    pattern: '^[a-z0-9-]+$',
    patternMessage: 'Slug can only contain lowercase letters, numbers, and hyphens'
  },
  description: { required: true, type: 'string', min: 10, max: 500 },
  content: { required: true, type: 'string', min: 50 },
  category: { required: true, type: 'string', min: 3, max: 50 },
  duration: { required: true, type: 'string', min: 3, max: 50 },
  price: { required: true, type: 'number', min: 0 },
  featured: { type: 'boolean' }
};

/**
 * @route POST /api/experiences
 * @desc Create a new experience
 * @access Private
 */
router.post('/', 
  authenticateToken, 
  uploadSingleFile('image'),
  validateBody(experienceSchema),
  (req, res, next) => {
    try {
      const { title, slug, description, content, category, duration, price, featured } = req.validatedData;
      
      // Process uploaded image if available
      let image_url = req.body.image_url; // Use existing URL if provided
      
      if (req.file) {
        const fileInfo = processUploadedFile(req, req.file);
        image_url = fileInfo.url;
      }

      // Check if slug already exists
      const existingExperience = Experience.getBySlug(slug);
      if (existingExperience) {
        throw new ValidationError('Slug already exists');
      }

      const newExperience = Experience.create({
        title,
        slug,
        description,
        content,
        image_url,
        category,
        duration,
        price: parseFloat(price),
        featured: featured || false
      });

      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('experience-created', newExperience);
      }

      res.status(201).json(newExperience);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route PUT /api/experiences/:id
 * @desc Update an experience
 * @access Private
 */
router.put('/:id', 
  authenticateToken, 
  uploadSingleFile('image'),
  validateBody(experienceSchema),
  (req, res, next) => {
    try {
      const experienceId = parseInt(req.params.id);
      const { title, slug, description, content, category, duration, price, featured } = req.validatedData;
      
      // Process uploaded image if available
      let image_url = req.body.image_url; // Use existing URL if provided
      
      if (req.file) {
        const fileInfo = processUploadedFile(req, req.file);
        image_url = fileInfo.url;
      }

      // Check if slug already exists and it's not the current experience
      const existingExperience = Experience.getBySlug(slug);
      if (existingExperience && existingExperience.id !== experienceId) {
        throw new ValidationError('Slug already exists');
      }

      const updatedExperience = Experience.update(experienceId, {
        title,
        slug,
        description,
        content,
        image_url,
        category,
        duration,
        price: parseFloat(price),
        featured
      });

      if (!updatedExperience) {
        throw new NotFoundError('Experience not found');
      }

      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('experience-updated', updatedExperience);
      }

      res.json(updatedExperience);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route DELETE /api/experiences/:id
 * @desc Delete an experience
 * @access Private (Admin)
 */
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const experienceId = parseInt(req.params.id);

    const deleted = Experience.deleteById(experienceId);

    if (!deleted) {
      throw new NotFoundError('Experience not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('experience-deleted', { id: experienceId });
    }

    res.json({ message: 'Experience deleted successfully' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;