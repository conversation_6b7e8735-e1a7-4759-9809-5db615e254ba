const express = require('express');
const router = express.Router();
const { Destination } = require('../database/models');
const { 
  authenticateToken, 
  requireAdmin, 
  validateBody, 
  schemas,
  NotFoundError,
  ValidationError,
  uploadSingleFile,
  processUploadedFile
} = require('../middleware');

/**
 * @route GET /api/destinations
 * @desc Get all destinations
 * @access Public
 */
router.get('/', (req, res, next) => {
  try {
    const destinations = Destination.getAll();
    res.json(destinations);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/destinations/featured
 * @desc Get featured destinations
 * @access Public
 */
router.get('/featured', (req, res, next) => {
  try {
    const featuredDestinations = Destination.getFeatured();
    res.json(featuredDestinations);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/destinations/category/:category
 * @desc Get destinations by category
 * @access Public
 */
router.get('/category/:category', (req, res, next) => {
  try {
    const { category } = req.params;
    const destinations = Destination.getByCategory(category);
    res.json(destinations);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/destinations/:id
 * @desc Get destination by ID
 * @access Public
 */
router.get('/:id', (req, res, next) => {
  try {
    const destination = Destination.getById(parseInt(req.params.id));

    if (!destination) {
      throw new NotFoundError('Destination not found');
    }

    res.json(destination);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/destinations/slug/:slug
 * @desc Get destination by slug
 * @access Public
 */
router.get('/slug/:slug', (req, res, next) => {
  try {
    const destination = Destination.getBySlug(req.params.slug);

    if (!destination) {
      throw new NotFoundError('Destination not found');
    }

    res.json(destination);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/destinations/:id/edit
 * @desc Get destination data for editing
 * @access Private
 */
router.get('/:id/edit', authenticateToken, (req, res, next) => {
  try {
    const destination = Destination.getById(parseInt(req.params.id));

    if (!destination) {
      throw new NotFoundError('Destination not found');
    }

    // Get available categories for dropdown options
    const allDestinations = Destination.getAll();
    const categories = [...new Set(allDestinations.map(d => d.category))];

    // Return the destination with additional metadata for editing
    res.json({
      item: destination,
      metadata: {
        categories,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: [
          'title', 'slug', 'description', 'content', 'image_url', 'category', 'featured'
        ]
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Schema for destination validation
 */
const destinationSchema = {
  title: { required: true, type: 'string', min: 3, max: 100 },
  slug: { 
    required: true, 
    type: 'string', 
    min: 3, 
    max: 100,
    pattern: '^[a-z0-9-]+$',
    patternMessage: 'Slug can only contain lowercase letters, numbers, and hyphens'
  },
  description: { required: true, type: 'string', min: 10, max: 500 },
  content: { required: true, type: 'string', min: 50 },
  category: { required: true, type: 'string', min: 3, max: 50 },
  featured: { type: 'boolean' }
};

/**
 * @route POST /api/destinations
 * @desc Create a new destination
 * @access Private
 */
router.post('/', 
  authenticateToken, 
  uploadSingleFile('image'),
  validateBody(destinationSchema),
  (req, res, next) => {
    try {
      const { title, slug, description, content, category, featured } = req.validatedData;
      
      // Process uploaded image if available
      let image_url = req.body.image_url; // Use existing URL if provided
      
      if (req.file) {
        const fileInfo = processUploadedFile(req, req.file);
        image_url = fileInfo.url;
      }

      // Check if slug already exists
      const existingDestination = Destination.getBySlug(slug);
      if (existingDestination) {
        throw new ValidationError('Slug already exists');
      }

      const newDestination = Destination.create({
        title,
        slug,
        description,
        content,
        image_url,
        category,
        featured: featured || false
      });

      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('destination-created', newDestination);
      }

      res.status(201).json(newDestination);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route PUT /api/destinations/:id
 * @desc Update a destination
 * @access Private
 */
router.put('/:id', 
  authenticateToken, 
  uploadSingleFile('image'),
  validateBody(destinationSchema),
  (req, res, next) => {
    try {
      const destinationId = parseInt(req.params.id);
      const { title, slug, description, content, category, featured } = req.validatedData;
      
      // Process uploaded image if available
      let image_url = req.body.image_url; // Use existing URL if provided
      
      if (req.file) {
        const fileInfo = processUploadedFile(req, req.file);
        image_url = fileInfo.url;
      }

      // Check if slug already exists and it's not the current destination
      const existingDestination = Destination.getBySlug(slug);
      if (existingDestination && existingDestination.id !== destinationId) {
        throw new ValidationError('Slug already exists');
      }

      const updatedDestination = Destination.update(destinationId, {
        title,
        slug,
        description,
        content,
        image_url,
        category,
        featured
      });

      if (!updatedDestination) {
        throw new NotFoundError('Destination not found');
      }

      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('destination-updated', updatedDestination);
      }

      res.json(updatedDestination);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route DELETE /api/destinations/:id
 * @desc Delete a destination
 * @access Private (Admin)
 */
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const destinationId = parseInt(req.params.id);

    const deleted = Destination.deleteById(destinationId);

    if (!deleted) {
      throw new NotFoundError('Destination not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('destination-deleted', { id: destinationId });
    }

    res.json({ message: 'Destination deleted successfully' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;