const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const { User } = require('../database/models');
const { 
  authenticateToken, 
  requireAdmin, 
  validateBody, 
  schemas,
  NotFoundError,
  ValidationError
} = require('../middleware');

/**
 * @route GET /api/users
 * @desc Get all users (admin only)
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const users = User.getAll();
    
    // Return users without passwords
    const usersWithoutPasswords = users.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });

    res.json(usersWithoutPasswords);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/users/:id
 * @desc Get user by ID
 * @access Private (Admin or own user)
 */
router.get('/:id', authenticateToken, (req, res, next) => {
  try {
    const userId = parseInt(req.params.id);
    const user = User.getById(userId);

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Check if the requesting user is the same as the requested user or is an admin
    if (req.user.id !== user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { password, ...userWithoutPassword } = user;
    res.json(userWithoutPassword);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/users/:id/edit
 * @desc Get user data for editing
 * @access Private (Admin or own user)
 */
router.get('/:id/edit', authenticateToken, (req, res, next) => {
  try {
    const userId = parseInt(req.params.id);
    
    // Check if user is admin or editing their own account
    if (req.user.role !== 'admin' && req.user.id !== userId) {
      return res.status(403).json({ 
        message: 'Access denied. Admin privileges required or you can only edit your own account.' 
      });
    }

    const user = User.getById(userId);

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Remove password from response
    const { password, ...userWithoutPassword } = user;

    // Get role options
    const roleOptions = ['admin', 'editor'];

    // Return the user with additional metadata for editing
    res.json({
      item: userWithoutPassword,
      metadata: {
        roleOptions,
        lastModified: new Date().toISOString(),
        canEdit: req.user.role === 'admin' || req.user.id === user.id,
        editableFields: req.user.role === 'admin'
          ? ['name', 'username', 'email', 'role']
          : ['name', 'email'],
        requiresPassword: false,
        canChangePassword: true
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/users
 * @desc Create a new user
 * @access Private (Admin)
 */
router.post('/', authenticateToken, requireAdmin, validateBody(schemas.createUser), async (req, res, next) => {
  try {
    const { username, password, name, email, role } = req.validatedData;

    // Check if username already exists
    const existingUser = User.getByUsername(username);
    if (existingUser) {
      throw new ValidationError('Username already exists');
    }
    
    // Check if email already exists
    const existingEmail = User.getByEmail(email);
    if (existingEmail) {
      throw new ValidationError('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = User.create({
      username,
      password: hashedPassword,
      name,
      email,
      role: role || 'editor'
    });

    const { password: _, ...userWithoutPassword } = newUser;

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('user-created', userWithoutPassword);
    }

    res.status(201).json(userWithoutPassword);
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/users/:id
 * @desc Update a user
 * @access Private (Admin or own user)
 */
router.put('/:id', authenticateToken, validateBody(schemas.updateUser), async (req, res, next) => {
  try {
    const userId = parseInt(req.params.id);

    // Check permissions
    if (req.user.role !== 'admin' && req.user.id !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { username, name, email, role } = req.validatedData;
    
    // If changing username, check if it already exists
    if (username) {
      const existingUser = User.getByUsername(username);
      if (existingUser && existingUser.id !== userId) {
        throw new ValidationError('Username already exists');
      }
    }
    
    // If changing email, check if it already exists
    if (email) {
      const existingEmail = User.getByEmail(email);
      if (existingEmail && existingEmail.id !== userId) {
        throw new ValidationError('Email already exists');
      }
    }
    
    // Only allow admin to change roles
    const updateData = {
      username,
      name,
      email
    };
    
    if (req.user.role === 'admin' && role) {
      updateData.role = role;
    }

    const updatedUser = User.update(userId, updateData);

    if (!updatedUser) {
      throw new NotFoundError('User not found');
    }

    const { password, ...userWithoutPassword } = updatedUser;

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('user-updated', userWithoutPassword);
    }

    res.json(userWithoutPassword);
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/users/:id/password
 * @desc Change user password
 * @access Private (Admin or own user)
 */
router.put('/:id/password', authenticateToken, async (req, res, next) => {
  try {
    const userId = parseInt(req.params.id);
    const { currentPassword, newPassword } = req.body;

    // Check permissions
    if (req.user.role !== 'admin' && req.user.id !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const user = User.getById(userId);

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // If not admin, verify current password
    if (req.user.role !== 'admin') {
      const isValidPassword = await bcrypt.compare(currentPassword, user.password);
      if (!isValidPassword) {
        throw new ValidationError('Current password is incorrect');
      }
    }

    // Validate new password
    if (!newPassword || newPassword.length < 8) {
      throw new ValidationError('New password must be at least 8 characters long');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password
    const updatedUser = User.update(userId, { password: hashedPassword });

    if (!updatedUser) {
      throw new NotFoundError('User not found');
    }

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/users/:id
 * @desc Delete a user
 * @access Private (Admin)
 */
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const userId = parseInt(req.params.id);

    // Prevent admin from deleting themselves
    if (req.user.id === userId) {
      throw new ValidationError('Cannot delete your own account');
    }

    const deleted = User.deleteById(userId);

    if (!deleted) {
      throw new NotFoundError('User not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('user-deleted', { id: userId });
    }

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;