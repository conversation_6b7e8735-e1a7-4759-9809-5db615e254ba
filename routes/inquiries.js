const express = require('express');
const router = express.Router();
const { Inquiry } = require('../database/models');
const { 
  authenticateToken, 
  requireAdmin, 
  validateBody, 
  schemas,
  NotFoundError,
  ValidationError
} = require('../middleware');

/**
 * @route GET /api/inquiries
 * @desc Get all inquiries
 * @access Private
 */
router.get('/', authenticateToken, (req, res, next) => {
  try {
    const inquiries = Inquiry.getAll();
    res.json(inquiries);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/inquiries/unread
 * @desc Get unread inquiries
 * @access Private
 */
router.get('/unread', authenticateToken, (req, res, next) => {
  try {
    const unreadInquiries = Inquiry.getUnread();
    res.json(unreadInquiries);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/inquiries/:id
 * @desc Get inquiry by ID
 * @access Private
 */
router.get('/:id', authenticateToken, (req, res, next) => {
  try {
    const inquiry = Inquiry.getById(parseInt(req.params.id));

    if (!inquiry) {
      throw new NotFoundError('Inquiry not found');
    }

    res.json(inquiry);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/inquiries/:id/edit
 * @desc Get inquiry data for editing
 * @access Private
 */
router.get('/:id/edit', authenticateToken, (req, res, next) => {
  try {
    const inquiry = Inquiry.getById(parseInt(req.params.id));

    if (!inquiry) {
      throw new NotFoundError('Inquiry not found');
    }

    // Get status options
    const statusOptions = ['unread', 'read', 'replied'];

    // Return the inquiry with additional metadata for editing
    res.json({
      item: inquiry,
      metadata: {
        statusOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: ['status', 'reply'],
        canReply: inquiry.status !== 'replied',
        replyTemplate: `Dear ${inquiry.name},\n\nThank you for your inquiry about ${inquiry.subject}.\n\n[Your response here]\n\nBest regards,\nLux Voyage Team`
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/inquiries
 * @desc Create a new inquiry
 * @access Public
 */
router.post('/', validateBody(schemas.createInquiry), (req, res, next) => {
  try {
    const { name, email, phone, subject, message } = req.validatedData;

    const newInquiry = Inquiry.create({
      name,
      email,
      phone: phone || '',
      subject: subject || 'General Inquiry',
      message
    });

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('inquiry-created', newInquiry);
    }

    res.status(201).json(newInquiry);
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/inquiries/:id/read
 * @desc Mark inquiry as read
 * @access Private
 */
router.put('/:id/read', authenticateToken, (req, res, next) => {
  try {
    const inquiryId = parseInt(req.params.id);

    const updatedInquiry = Inquiry.markAsRead(inquiryId);

    if (!updatedInquiry) {
      throw new NotFoundError('Inquiry not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('inquiry-updated', updatedInquiry);
    }

    res.json(updatedInquiry);
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/inquiries/:id/reply
 * @desc Reply to an inquiry
 * @access Private
 */
router.post('/:id/reply', authenticateToken, validateBody(schemas.replyInquiry), (req, res, next) => {
  try {
    const inquiryId = parseInt(req.params.id);
    const { reply } = req.validatedData;

    const updatedInquiry = Inquiry.reply(inquiryId, reply, req.user.id);

    if (!updatedInquiry) {
      throw new NotFoundError('Inquiry not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('inquiry-replied', updatedInquiry);
    }

    res.json(updatedInquiry);
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/inquiries/:id
 * @desc Delete an inquiry
 * @access Private (Admin)
 */
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const inquiryId = parseInt(req.params.id);

    const deleted = Inquiry.deleteById(inquiryId);

    if (!deleted) {
      throw new NotFoundError('Inquiry not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('inquiry-deleted', { id: inquiryId });
    }

    res.json({ message: 'Inquiry deleted successfully' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;