const express = require('express');
const router = express.Router();
const { Settings } = require('../database/models');
const { 
  authenticateToken, 
  requireAdmin, 
  NotFoundError,
  ValidationError
} = require('../middleware');

/**
 * @route GET /api/settings
 * @desc Get all settings
 * @access Public
 */
router.get('/', (req, res, next) => {
  try {
    const settings = Settings.getAll();
    
    // Group settings by group_name for easier consumption
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.group_name]) {
        acc[setting.group_name] = {};
      }
      acc[setting.group_name][setting.key_name] = setting.value;
      return acc;
    }, {});
    
    res.json(groupedSettings);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/settings/raw
 * @desc Get all settings in raw format (not grouped)
 * @access Private (Admin)
 */
router.get('/raw', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const settings = Settings.getAll();
    res.json(settings);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/settings/group/:group
 * @desc Get settings by group
 * @access Public
 */
router.get('/group/:group', (req, res, next) => {
  try {
    const { group } = req.params;
    const settings = Settings.getByGroup(group);
    
    // Convert to key-value object
    const groupSettings = settings.reduce((acc, setting) => {
      acc[setting.key_name] = setting.value;
      return acc;
    }, {});
    
    res.json(groupSettings);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/settings/:key
 * @desc Get setting by key
 * @access Public
 */
router.get('/:key', (req, res, next) => {
  try {
    const setting = Settings.getByKey(req.params.key);

    if (!setting) {
      throw new NotFoundError('Setting not found');
    }

    res.json(setting);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/settings/:key/edit
 * @desc Get setting data for editing
 * @access Private (Admin)
 */
router.get('/:key/edit', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const setting = Settings.getByKey(req.params.key);

    if (!setting) {
      throw new NotFoundError('Setting not found');
    }

    // Determine value type and provide appropriate options
    let valueOptions = null;
    let valueType = 'text';

    if (setting.key_name === 'primaryColor' || setting.key_name === 'secondaryColor') {
      valueType = 'color';
    } else if (setting.key_name === 'maintenanceMode') {
      valueType = 'boolean';
      valueOptions = [
        { value: 'true', label: 'Enabled' },
        { value: 'false', label: 'Disabled' }
      ];
    } else if (setting.key_name === 'buttonStyle') {
      valueType = 'select';
      valueOptions = [
        { value: 'rounded', label: 'Rounded' },
        { value: 'square', label: 'Square' },
        { value: 'pill', label: 'Pill' }
      ];
    } else if (setting.key_name === 'fontFamily' || setting.key_name === 'headingFontFamily') {
      valueType = 'select';
      valueOptions = [
        { value: 'Inter', label: 'Inter' },
        { value: 'Roboto', label: 'Roboto' },
        { value: 'Open Sans', label: 'Open Sans' },
        { value: 'Cormorant Garamond', label: 'Cormorant Garamond' },
        { value: 'Playfair Display', label: 'Playfair Display' }
      ];
    }

    // Return the setting with additional metadata for editing
    res.json({
      item: setting,
      metadata: {
        valueType,
        valueOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        group: setting.group_name,
        description: `Setting for ${setting.key_name} in the ${setting.group_name} group`
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/settings/:key
 * @desc Update a setting
 * @access Private (Admin)
 */
router.put('/:key', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { value } = req.body;

    if (value === undefined || value === null) {
      throw new ValidationError('Value is required');
    }

    const updated = Settings.updateByKey(req.params.key, value);

    if (!updated) {
      throw new NotFoundError('Setting not found');
    }

    const setting = Settings.getByKey(req.params.key);

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('setting-updated', setting);
    }

    res.json(setting);
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/settings
 * @desc Create a new setting
 * @access Private (Admin)
 */
router.post('/', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { group, key, value } = req.body;

    if (!group || !key || value === undefined) {
      throw new ValidationError('Group, key, and value are required');
    }

    // Check if setting already exists
    const existingSetting = Settings.getByKey(key);
    if (existingSetting) {
      throw new ValidationError('Setting already exists');
    }

    const newSetting = Settings.create(group, key, value);

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('setting-created', newSetting);
    }

    res.status(201).json(newSetting);
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/settings/bulk
 * @desc Update multiple settings at once
 * @access Private (Admin)
 */
router.put('/bulk', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { settings } = req.body;

    if (!settings || !Array.isArray(settings)) {
      throw new ValidationError('Settings array is required');
    }

    const updatedSettings = [];
    const errors = [];

    // Update each setting
    for (const setting of settings) {
      if (!setting.key || setting.value === undefined) {
        errors.push(`Invalid setting: ${JSON.stringify(setting)}`);
        continue;
      }

      try {
        const updated = Settings.updateByKey(setting.key, setting.value);
        if (updated) {
          updatedSettings.push(Settings.getByKey(setting.key));
        } else {
          errors.push(`Setting not found: ${setting.key}`);
        }
      } catch (error) {
        errors.push(`Error updating ${setting.key}: ${error.message}`);
      }
    }

    // Emit real-time update if socket.io is available
    if (req.io && updatedSettings.length > 0) {
      req.io.to('admin').emit('settings-bulk-updated', updatedSettings);
    }

    res.json({
      updated: updatedSettings,
      errors: errors.length > 0 ? errors : undefined,
      success: updatedSettings.length > 0
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;