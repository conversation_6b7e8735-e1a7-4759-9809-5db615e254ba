const express = require('express');
const router = express.Router();
const { Offer } = require('../database/models');
const { 
  authenticateToken, 
  requireAdmin, 
  validateBody, 
  NotFoundError,
  ValidationError,
  uploadSingleFile,
  processUploadedFile
} = require('../middleware');

/**
 * @route GET /api/offers
 * @desc Get all offers
 * @access Public
 */
router.get('/', (req, res, next) => {
  try {
    const offers = Offer.getAll();
    res.json(offers);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/offers/active
 * @desc Get active offers (valid date range)
 * @access Public
 */
router.get('/active', (req, res, next) => {
  try {
    const activeOffers = Offer.getActive();
    res.json(activeOffers);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/offers/featured
 * @desc Get featured offers
 * @access Public
 */
router.get('/featured', (req, res, next) => {
  try {
    const featuredOffers = Offer.getFeatured();
    res.json(featuredOffers);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/offers/:id
 * @desc Get offer by ID
 * @access Public
 */
router.get('/:id', (req, res, next) => {
  try {
    const offer = Offer.getById(parseInt(req.params.id));

    if (!offer) {
      throw new NotFoundError('Offer not found');
    }

    res.json(offer);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/offers/slug/:slug
 * @desc Get offer by slug
 * @access Public
 */
router.get('/slug/:slug', (req, res, next) => {
  try {
    const offer = Offer.getBySlug(req.params.slug);

    if (!offer) {
      throw new NotFoundError('Offer not found');
    }

    res.json(offer);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/offers/:id/edit
 * @desc Get offer data for editing
 * @access Private
 */
router.get('/:id/edit', authenticateToken, (req, res, next) => {
  try {
    const offer = Offer.getById(parseInt(req.params.id));

    if (!offer) {
      throw new NotFoundError('Offer not found');
    }

    // Get discount options
    const discountOptions = [5, 10, 15, 20, 25, 30, 40, 50];

    // Return the offer with additional metadata for editing
    res.json({
      item: offer,
      metadata: {
        discountOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: [
          'title', 'slug', 'description', 'content', 'image_url',
          'discount', 'valid_from', 'valid_to', 'featured'
        ],
        validationRules: {
          valid_to: 'must be after valid_from',
          discount: 'must be between 1 and 100'
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Schema for offer validation
 */
const offerSchema = {
  title: { required: true, type: 'string', min: 3, max: 100 },
  slug: { 
    required: true, 
    type: 'string', 
    min: 3, 
    max: 100,
    pattern: '^[a-z0-9-]+$',
    patternMessage: 'Slug can only contain lowercase letters, numbers, and hyphens'
  },
  description: { required: true, type: 'string', min: 10, max: 500 },
  content: { required: true, type: 'string', min: 50 },
  discount: { required: true, type: 'number', min: 1, max: 100 },
  valid_from: { required: true, type: 'date' },
  valid_to: { 
    required: true, 
    type: 'date',
    validate: (value, body) => {
      const validFrom = new Date(body.valid_from);
      const validTo = new Date(value);
      return validTo > validFrom || 'Valid to date must be after valid from date';
    }
  },
  featured: { type: 'boolean' }
};

/**
 * @route POST /api/offers
 * @desc Create a new offer
 * @access Private
 */
router.post('/', 
  authenticateToken, 
  uploadSingleFile('image'),
  validateBody(offerSchema),
  (req, res, next) => {
    try {
      const { title, slug, description, content, discount, valid_from, valid_to, featured } = req.validatedData;
      
      // Process uploaded image if available
      let image_url = req.body.image_url; // Use existing URL if provided
      
      if (req.file) {
        const fileInfo = processUploadedFile(req, req.file);
        image_url = fileInfo.url;
      }

      // Check if slug already exists
      const existingOffer = Offer.getBySlug(slug);
      if (existingOffer) {
        throw new ValidationError('Slug already exists');
      }

      const newOffer = Offer.create({
        title,
        slug,
        description,
        content,
        image_url,
        discount: parseFloat(discount),
        valid_from,
        valid_to,
        featured: featured || false
      });

      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('offer-created', newOffer);
      }

      res.status(201).json(newOffer);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route PUT /api/offers/:id
 * @desc Update an offer
 * @access Private
 */
router.put('/:id', 
  authenticateToken, 
  uploadSingleFile('image'),
  validateBody(offerSchema),
  (req, res, next) => {
    try {
      const offerId = parseInt(req.params.id);
      const { title, slug, description, content, discount, valid_from, valid_to, featured } = req.validatedData;
      
      // Process uploaded image if available
      let image_url = req.body.image_url; // Use existing URL if provided
      
      if (req.file) {
        const fileInfo = processUploadedFile(req, req.file);
        image_url = fileInfo.url;
      }

      // Check if slug already exists and it's not the current offer
      const existingOffer = Offer.getBySlug(slug);
      if (existingOffer && existingOffer.id !== offerId) {
        throw new ValidationError('Slug already exists');
      }

      const updatedOffer = Offer.update(offerId, {
        title,
        slug,
        description,
        content,
        image_url,
        discount: parseFloat(discount),
        valid_from,
        valid_to,
        featured
      });

      if (!updatedOffer) {
        throw new NotFoundError('Offer not found');
      }

      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('offer-updated', updatedOffer);
      }

      res.json(updatedOffer);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route DELETE /api/offers/:id
 * @desc Delete an offer
 * @access Private (Admin)
 */
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const offerId = parseInt(req.params.id);

    const deleted = Offer.deleteById(offerId);

    if (!deleted) {
      throw new NotFoundError('Offer not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('offer-deleted', { id: offerId });
    }

    res.json({ message: 'Offer deleted successfully' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;