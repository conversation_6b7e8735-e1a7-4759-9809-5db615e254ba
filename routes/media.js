const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { Media } = require('../database/models');
const { 
  authenticateToken, 
  requireAdmin, 
  uploadSingleFile,
  processUploadedFile,
  NotFoundError,
  ValidationError
} = require('../middleware');

/**
 * @route GET /api/media
 * @desc Get all media
 * @access Public
 */
router.get('/', (req, res, next) => {
  try {
    const media = Media.getAll();
    res.json(media);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/media/:id
 * @desc Get media by ID
 * @access Public
 */
router.get('/:id', (req, res, next) => {
  try {
    const media = Media.getById(parseInt(req.params.id));

    if (!media) {
      throw new NotFoundError('Media not found');
    }

    res.json(media);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/media/:id/edit
 * @desc Get media data for editing
 * @access Private
 */
router.get('/:id/edit', authenticateToken, (req, res, next) => {
  try {
    const media = Media.getById(parseInt(req.params.id));

    if (!media) {
      throw new NotFoundError('Media not found');
    }

    // Get media type options
    const typeOptions = ['image', 'video', 'document'];

    // Return the media with additional metadata for editing
    res.json({
      item: media,
      metadata: {
        typeOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: ['title', 'alt', 'description'],
        fileInfo: {
          url: media.url,
          type: media.type,
          size: media.size,
          dimensions: media.type === 'image' ? '1920x1080' : null
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Schema for media validation
 */
const mediaSchema = {
  title: { required: true, type: 'string', min: 3, max: 100 },
  alt: { type: 'string', max: 200 },
  description: { type: 'string', max: 500 }
};

/**
 * @route POST /api/media/upload
 * @desc Upload a new media file
 * @access Private
 */
router.post('/upload', 
  authenticateToken, 
  uploadSingleFile('file'),
  (req, res, next) => {
    try {
      if (!req.file) {
        throw new ValidationError('No file uploaded');
      }

      console.log('File uploaded successfully:', req.file.filename);
      
      const fileInfo = processUploadedFile(req, req.file);
      
      const newMedia = Media.create({
        title: req.body.title || req.file.originalname,
        alt: req.body.alt || req.file.originalname,
        description: req.body.description || '',
        url: fileInfo.url,
        type: fileInfo.type,
        size: fileInfo.size,
        created_by: req.user.id
      });

      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('media-uploaded', newMedia);
      }

      res.status(201).json(newMedia);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route PUT /api/media/:id
 * @desc Update media metadata
 * @access Private
 */
router.put('/:id', authenticateToken, (req, res, next) => {
  try {
    const mediaId = parseInt(req.params.id);
    const { title, alt, description } = req.body;

    // Validate required fields
    if (!title) {
      throw new ValidationError('Title is required');
    }

    const updatedMedia = Media.update(mediaId, {
      title,
      alt: alt || '',
      description: description || ''
    });

    if (!updatedMedia) {
      throw new NotFoundError('Media not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('media-updated', updatedMedia);
    }

    res.json(updatedMedia);
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/media/:id
 * @desc Delete media
 * @access Private (Admin)
 */
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const mediaId = parseInt(req.params.id);

    // Get media info before deletion to remove file
    const media = Media.getById(mediaId);

    if (!media) {
      throw new NotFoundError('Media not found');
    }

    // Delete the file from the uploads directory
    const filePath = path.join(__dirname, '..', media.url);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    const deleted = Media.deleteById(mediaId);

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('media-deleted', { id: mediaId });
    }

    res.json({ message: 'Media deleted successfully' });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/media/type/:type
 * @desc Get media by type (image, video, document)
 * @access Public
 */
router.get('/type/:type', (req, res, next) => {
  try {
    const { type } = req.params;
    
    // Validate type
    const validTypes = ['image', 'video', 'document'];
    if (!validTypes.includes(type)) {
      throw new ValidationError(`Invalid media type. Must be one of: ${validTypes.join(', ')}`);
    }
    
    // Filter media by type
    const allMedia = Media.getAll();
    const filteredMedia = allMedia.filter(item => item.type === type);
    
    res.json(filteredMedia);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/media/search
 * @desc Search media by title or description
 * @access Public
 */
router.get('/search', (req, res, next) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.json([]);
    }
    
    // Search media by title or description
    const allMedia = Media.getAll();
    const searchResults = allMedia.filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase()) || 
      (item.description && item.description.toLowerCase().includes(query.toLowerCase()))
    );
    
    res.json(searchResults);
  } catch (error) {
    next(error);
  }
});

module.exports = router;