const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User } = require('../database/models');
const { 
  validateBody, 
  schemas,
  UnauthorizedError,
  ValidationError
} = require('../middleware');

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

/**
 * @route POST /api/auth/login
 * @desc Authenticate user & get token
 * @access Public
 */
router.post('/login', validateBody(schemas.login), async (req, res, next) => {
  try {
    const { username, password } = req.validatedData;

    // Check if user exists
    const user = User.getByUsername(username);
    if (!user) {
      throw new UnauthorizedError('Invalid username or password');
    }

    // Compare password with hashed password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      throw new UnauthorizedError('Invalid username or password');
    }

    // Create JWT token
    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Return user info (without password) and token
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      message: 'Login successful',
      user: userWithoutPassword,
      token
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/auth/verify
 * @desc Verify token and return user data
 * @access Public
 */
router.post('/verify', (req, res, next) => {
  try {
    const { token } = req.body;

    if (!token) {
      throw new ValidationError('Token is required');
    }

    // Verify token
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        if (err.name === 'TokenExpiredError') {
          return res.status(401).json({ 
            valid: false, 
            message: 'Token expired', 
            expired: true 
          });
        }
        return res.status(401).json({ 
          valid: false, 
          message: 'Invalid token' 
        });
      }

      // Get user data
      const user = User.getById(decoded.id);
      if (!user) {
        return res.status(404).json({ 
          valid: false, 
          message: 'User not found' 
        });
      }

      // Return user info (without password)
      const { password, ...userWithoutPassword } = user;
      res.json({
        valid: true,
        user: userWithoutPassword
      });
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/auth/refresh
 * @desc Refresh token
 * @access Public
 */
router.post('/refresh', (req, res, next) => {
  try {
    const { token } = req.body;

    if (!token) {
      throw new ValidationError('Token is required');
    }

    // Verify token
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        // If token is expired, we can still refresh it if it's not too old
        if (err.name === 'TokenExpiredError') {
          try {
            // Decode without verification to get user info
            const decodedAnyway = jwt.decode(token);
            
            if (!decodedAnyway || !decodedAnyway.id) {
              return res.status(401).json({ 
                message: 'Invalid token format' 
              });
            }
            
            // Check if token expired more than 30 days ago
            const expiry = new Date(decodedAnyway.exp * 1000);
            const now = new Date();
            const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
            
            if ((now - expiry) > thirtyDaysInMs) {
              return res.status(401).json({ 
                message: 'Token expired too long ago, please login again' 
              });
            }
            
            // Get user data
            const user = User.getById(decodedAnyway.id);
            if (!user) {
              return res.status(404).json({ 
                message: 'User not found' 
              });
            }
            
            // Create new token
            const newToken = jwt.sign(
              { id: user.id, username: user.username, role: user.role },
              JWT_SECRET,
              { expiresIn: JWT_EXPIRES_IN }
            );
            
            // Return user info (without password) and new token
            const { password, ...userWithoutPassword } = user;
            return res.json({
              message: 'Token refreshed',
              user: userWithoutPassword,
              token: newToken
            });
          } catch (decodeError) {
            return res.status(401).json({ 
              message: 'Failed to decode expired token' 
            });
          }
        }
        
        return res.status(401).json({ 
          message: 'Invalid token' 
        });
      }

      // Token is still valid, get user data
      const user = User.getById(decoded.id);
      if (!user) {
        return res.status(404).json({ 
          message: 'User not found' 
        });
      }

      // Create new token
      const newToken = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
      );

      // Return user info (without password) and new token
      const { password, ...userWithoutPassword } = user;
      res.json({
        message: 'Token refreshed',
        user: userWithoutPassword,
        token: newToken
      });
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/auth/change-password
 * @desc Change user password
 * @access Public
 */
router.post('/change-password', async (req, res, next) => {
  try {
    const { username, currentPassword, newPassword } = req.body;

    if (!username || !currentPassword || !newPassword) {
      throw new ValidationError('Username, current password, and new password are required');
    }

    // Check if user exists
    const user = User.getByUsername(username);
    if (!user) {
      throw new UnauthorizedError('Invalid username or password');
    }

    // Compare password with hashed password
    const isValidPassword = await bcrypt.compare(currentPassword, user.password);
    if (!isValidPassword) {
      throw new UnauthorizedError('Invalid username or password');
    }

    // Validate new password
    if (newPassword.length < 8) {
      throw new ValidationError('New password must be at least 8 characters long');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password
    const updatedUser = User.update(user.id, { password: hashedPassword });

    if (!updatedUser) {
      throw new Error('Failed to update password');
    }

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;