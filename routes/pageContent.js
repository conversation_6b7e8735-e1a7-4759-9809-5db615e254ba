const express = require('express');
const router = express.Router();
const { db } = require('../database/init');
const { 
  authenticateToken, 
  validateBody, 
  NotFoundError,
  ValidationError
} = require('../middleware');

/**
 * Schema for page content validation
 */
const pageContentSchema = {
  content: { required: true },
  metaTitle: { type: 'string', max: 100 },
  metaDescription: { type: 'string', max: 200 }
};

const createPageContentSchema = {
  pageName: { 
    required: true, 
    type: 'string', 
    min: 3, 
    max: 100,
    pattern: '^[a-z0-9-]+$',
    patternMessage: 'Page name can only contain lowercase letters, numbers, and hyphens'
  },
  content: { required: true },
  metaTitle: { type: 'string', max: 100 },
  metaDescription: { type: 'string', max: 200 }
};

/**
 * @route GET /api/page-content/:pageName
 * @desc Get page content by page name
 * @access Public
 */
router.get('/:pageName', (req, res, next) => {
  try {
    const { pageName } = req.params;
    
    const pageContent = db.prepare('SELECT * FROM page_content WHERE page_name = ?').get(pageName);
    
    if (!pageContent) {
      throw new NotFoundError('Page content not found');
    }

    // Parse JSON content
    const content = JSON.parse(pageContent.content);
    
    res.json({
      id: pageContent.id,
      pageName: pageContent.page_name,
      content,
      metaTitle: pageContent.meta_title,
      metaDescription: pageContent.meta_description,
      createdAt: pageContent.created_at,
      updatedAt: pageContent.updated_at
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/page-content
 * @desc Get all page content (admin only)
 * @access Private
 */
router.get('/', authenticateToken, (req, res, next) => {
  try {
    const pageContents = db.prepare('SELECT * FROM page_content ORDER BY page_name').all();
    
    const formattedContents = pageContents.map(page => ({
      id: page.id,
      pageName: page.page_name,
      content: JSON.parse(page.content),
      metaTitle: page.meta_title,
      metaDescription: page.meta_description,
      createdAt: page.created_at,
      updatedAt: page.updated_at
    }));
    
    res.json(formattedContents);
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/page-content/:pageName
 * @desc Update page content (admin only)
 * @access Private
 */
router.put('/:pageName', 
  authenticateToken, 
  validateBody(pageContentSchema),
  (req, res, next) => {
    try {
      const { pageName } = req.params;
      const { content, metaTitle, metaDescription } = req.validatedData;

      console.log(`[DEBUG] PUT /pageContent/${pageName} - Received request`);
      console.log(`[DEBUG] pageName: "${pageName}"`);
      console.log(`[DEBUG] metaTitle: "${metaTitle}"`);
      console.log(`[DEBUG] metaDescription: "${metaDescription}"`);

      // Convert content to JSON string if it's an object
      const contentString = typeof content === 'string' ? content : JSON.stringify(content);

      console.log('[DEBUG] Attempting to UPDATE page_content...');
      const updateStmt = db.prepare(`
        UPDATE page_content 
        SET content = ?, meta_title = ?, meta_description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE page_name = ?
      `);

      const result = updateStmt.run(contentString, metaTitle, metaDescription, pageName);
      console.log('[DEBUG] UPDATE result:', result);

      if (result.changes === 0) {
        console.log('[DEBUG] UPDATE affected 0 rows. Attempting to INSERT new page content...');
        // If no rows were updated, create new page content
        const insertStmt = db.prepare(`
          INSERT INTO page_content (page_name, content, meta_title, meta_description)
          VALUES (?, ?, ?, ?)
        `);
        try {
          const insertResult = insertStmt.run(pageName, contentString, metaTitle, metaDescription);
          console.log('[DEBUG] INSERT result:', insertResult);
        } catch (insertError) {
          console.error('[DEBUG] Error during INSERT:', insertError);
          if (insertError.code === 'SQLITE_CONSTRAINT_UNIQUE') {
            throw new ValidationError('Page content already exists');
          }
          throw insertError;
        }
      }

      // Return updated content
      console.log('[DEBUG] Fetching content after update/insert attempt...');
      const updatedContent = db.prepare('SELECT * FROM page_content WHERE page_name = ?').get(pageName);
      console.log('[DEBUG] Fetched content:', updatedContent);
      
      if (!updatedContent) {
        console.log('[DEBUG] Content still not found after update/insert attempt.');
        throw new NotFoundError('Page content not found after update attempt');
      }
      
      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('page-content-updated', {
          pageName,
          content: JSON.parse(updatedContent.content)
        });
      }
      
      res.json({
        id: updatedContent.id,
        pageName: updatedContent.page_name,
        content: JSON.parse(updatedContent.content),
        metaTitle: updatedContent.meta_title,
        metaDescription: updatedContent.meta_description,
        createdAt: updatedContent.created_at,
        updatedAt: updatedContent.updated_at
      });
    } catch (error) {
      console.error('[DEBUG] Error in PUT /pageContent/:pageName handler:', error);
      next(error);
    }
  }
);

/**
 * @route POST /api/page-content
 * @desc Create new page content (admin only)
 * @access Private
 */
router.post('/', 
  authenticateToken, 
  validateBody(createPageContentSchema),
  (req, res, next) => {
    try {
      const { pageName, content, metaTitle, metaDescription } = req.validatedData;

      // Convert content to JSON string if it's an object
      const contentString = typeof content === 'string' ? content : JSON.stringify(content);

      // Check if page content already exists
      const existingContent = db.prepare('SELECT id FROM page_content WHERE page_name = ?').get(pageName);
      if (existingContent) {
        throw new ValidationError('Page content already exists');
      }

      const insertStmt = db.prepare(`
        INSERT INTO page_content (page_name, content, meta_title, meta_description)
        VALUES (?, ?, ?, ?)
      `);

      const result = insertStmt.run(pageName, contentString, metaTitle, metaDescription);

      // Return created content
      const newContent = db.prepare('SELECT * FROM page_content WHERE id = ?').get(result.lastInsertRowid);
      
      // Emit real-time update if socket.io is available
      if (req.io) {
        req.io.to('admin').emit('page-content-created', {
          pageName,
          content: JSON.parse(newContent.content)
        });
      }
      
      res.status(201).json({
        id: newContent.id,
        pageName: newContent.page_name,
        content: JSON.parse(newContent.content),
        metaTitle: newContent.meta_title,
        metaDescription: newContent.meta_description,
        createdAt: newContent.created_at,
        updatedAt: newContent.updated_at
      });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route DELETE /api/page-content/:pageName
 * @desc Delete page content (admin only)
 * @access Private
 */
router.delete('/:pageName', authenticateToken, (req, res, next) => {
  try {
    const { pageName } = req.params;

    const deleteStmt = db.prepare('DELETE FROM page_content WHERE page_name = ?');
    const result = deleteStmt.run(pageName);

    if (result.changes === 0) {
      throw new NotFoundError('Page content not found');
    }

    // Emit real-time update if socket.io is available
    if (req.io) {
      req.io.to('admin').emit('page-content-deleted', { pageName });
    }

    res.json({ message: 'Page content deleted successfully' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
