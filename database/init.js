const { Pool } = require('pg');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'lux_voyage',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
};

// Create database pool
const pool = new Pool(dbConfig);

// Create tables
const createTables = async () => {
  const client = await pool.connect();
  
  try {
    // Users table
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        role VARCHAR(50) DEFAULT 'editor' CHECK (role IN ('admin', 'editor')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Destinations table
    await client.query(`
      CREATE TABLE IF NOT EXISTS destinations (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        content TEXT,
        image_url TEXT,
        category VARCHAR(100) NOT NULL,
        featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Experiences table
    await client.query(`
      CREATE TABLE IF NOT EXISTS experiences (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        content TEXT,
        image_url TEXT,
        category VARCHAR(100) NOT NULL,
        duration VARCHAR(100),
        price DECIMAL(10,2),
        featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Offers table
    await client.query(`
      CREATE TABLE IF NOT EXISTS offers (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        content TEXT,
        image_url TEXT,
        discount INTEGER NOT NULL,
        valid_from DATE NOT NULL,
        valid_to DATE NOT NULL,
        featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Inquiries table
    await client.query(`
      CREATE TABLE IF NOT EXISTS inquiries (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        subject VARCHAR(255),
        message TEXT NOT NULL,
        status VARCHAR(50) DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'replied')),
        reply TEXT,
        replied_at TIMESTAMP,
        replied_by INTEGER REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Media table
    await client.query(`
      CREATE TABLE IF NOT EXISTS media (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        alt VARCHAR(255),
        description TEXT,
        url TEXT NOT NULL,
        type VARCHAR(50) NOT NULL CHECK (type IN ('image', 'video', 'document')),
        size VARCHAR(50),
        created_by INTEGER REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Settings table
    await client.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id SERIAL PRIMARY KEY,
        group_name VARCHAR(100) NOT NULL,
        key_name VARCHAR(100) NOT NULL,
        value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(group_name, key_name)
      )
    `);

    // Page content table
    await client.query(`
      CREATE TABLE IF NOT EXISTS page_content (
        id SERIAL PRIMARY KEY,
        page_name VARCHAR(255) NOT NULL UNIQUE,
        content TEXT NOT NULL,
        meta_title VARCHAR(255),
        meta_description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('Database tables created successfully');
  } finally {
    client.release();
  }
};

// Insert default data
const insertDefaultData = async () => {
  const client = await pool.connect();
  
  try {
    // Check if users exist
    const userCount = await client.query('SELECT COUNT(*) as count FROM users');
    
    if (parseInt(userCount.rows[0].count) === 0) {
      // Hash passwords before inserting
      const adminPassword = await bcrypt.hash('admin123', 10);
      const editorPassword = await bcrypt.hash('editor123', 10);

      // Insert default users
      await client.query(`
        INSERT INTO users (username, password, name, email, role)
        VALUES ($1, $2, $3, $4, $5)
      `, ['admin', adminPassword, 'Admin User', '<EMAIL>', 'admin']);

      await client.query(`
        INSERT INTO users (username, password, name, email, role)
        VALUES ($1, $2, $3, $4, $5)
      `, ['editor', editorPassword, 'Editor User', '<EMAIL>', 'editor']);

      console.log('Default users inserted');
    }

    // Insert default settings
    const settingsCount = await client.query('SELECT COUNT(*) as count FROM settings');
    
    if (parseInt(settingsCount.rows[0].count) === 0) {
      const defaultSettings = [
        ['general', 'siteTitle', 'Lux Voyage Travel Agency'],
        ['general', 'siteDescription', 'Premium travel experiences and luxury destinations'],
        ['general', 'logoUrl', '/images/logo.png'],
        ['general', 'faviconUrl', '/favicon.ico'],
        ['general', 'defaultLanguage', 'en'],
        ['general', 'maintenanceMode', 'false'],
        ['general', 'contactEmail', '<EMAIL>'],
        ['general', 'contactPhone', '******-LUX-VOYAGE'],
        ['appearance', 'primaryColor', '#0099b8'],
        ['appearance', 'secondaryColor', '#ff9800'],
        ['appearance', 'fontFamily', 'Inter'],
        ['appearance', 'headingFontFamily', 'Cormorant Garamond'],
        ['appearance', 'buttonStyle', 'rounded'],
        ['content', 'heroTitle', 'Welcome to Lux Voyage'],
        ['content', 'heroSubtitle', 'Discover extraordinary travel experiences']
      ];

      for (const [group, key, value] of defaultSettings) {
        await client.query(`
          INSERT INTO settings (group_name, key_name, value)
          VALUES ($1, $2, $3)
        `, [group, key, value]);
      }

      console.log('Default settings inserted');
    }
  } finally {
    client.release();
  }
};

// Initialize database
const initializeDatabase = async () => {
  try {
    await createTables();
    await insertDefaultData();
    console.log('Database initialized successfully');
    return pool;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
};

module.exports = { initializeDatabase, pool };
