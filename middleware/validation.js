const { ValidationError } = require('./errorHandler');

/**
 * Validates request body against a schema
 * @param {Object} schema - Schema object with field definitions
 * @returns {Function} Express middleware function
 */
const validateBody = (schema) => {
  return (req, res, next) => {
    const errors = {};
    const validatedData = {};
    
    // Check each field in the schema
    for (const [field, rules] of Object.entries(schema)) {
      const value = req.body[field];
      
      // Check required fields
      if (rules.required && (value === undefined || value === null || value === '')) {
        errors[field] = `${field} is required`;
        continue;
      }
      
      // Skip validation for undefined optional fields
      if (value === undefined || value === null) {
        continue;
      }
      
      // Type validation
      if (rules.type) {
        let valid = true;
        let typedValue = value;
        
        switch (rules.type) {
          case 'string':
            valid = typeof value === 'string';
            break;
          case 'number':
            if (typeof value === 'string') {
              const num = Number(value);
              if (!isNaN(num)) {
                typedValue = num;
                valid = true;
              } else {
                valid = false;
              }
            } else {
              valid = typeof value === 'number' && !isNaN(value);
            }
            break;
          case 'boolean':
            if (typeof value === 'string') {
              if (value.toLowerCase() === 'true') {
                typedValue = true;
                valid = true;
              } else if (value.toLowerCase() === 'false') {
                typedValue = false;
                valid = true;
              } else {
                valid = false;
              }
            } else {
              valid = typeof value === 'boolean';
            }
            break;
          case 'array':
            valid = Array.isArray(value);
            break;
          case 'object':
            valid = typeof value === 'object' && !Array.isArray(value) && value !== null;
            break;
          case 'date':
            const date = new Date(value);
            valid = !isNaN(date.getTime());
            if (valid) {
              typedValue = date;
            }
            break;
        }
        
        if (!valid) {
          errors[field] = `${field} must be a valid ${rules.type}`;
          continue;
        }
        
        validatedData[field] = typedValue;
      } else {
        validatedData[field] = value;
      }
      
      // Min/max validation for strings and arrays
      if (rules.min !== undefined && (typeof value === 'string' || Array.isArray(value))) {
        if (value.length < rules.min) {
          errors[field] = `${field} must be at least ${rules.min} characters long`;
          continue;
        }
      }
      
      if (rules.max !== undefined && (typeof value === 'string' || Array.isArray(value))) {
        if (value.length > rules.max) {
          errors[field] = `${field} must be at most ${rules.max} characters long`;
          continue;
        }
      }
      
      // Min/max validation for numbers
      if (rules.min !== undefined && typeof validatedData[field] === 'number') {
        if (validatedData[field] < rules.min) {
          errors[field] = `${field} must be at least ${rules.min}`;
          continue;
        }
      }
      
      if (rules.max !== undefined && typeof validatedData[field] === 'number') {
        if (validatedData[field] > rules.max) {
          errors[field] = `${field} must be at most ${rules.max}`;
          continue;
        }
      }
      
      // Pattern validation
      if (rules.pattern && typeof value === 'string') {
        const regex = new RegExp(rules.pattern);
        if (!regex.test(value)) {
          errors[field] = rules.patternMessage || `${field} has an invalid format`;
          continue;
        }
      }
      
      // Enum validation
      if (rules.enum && Array.isArray(rules.enum)) {
        if (!rules.enum.includes(value)) {
          errors[field] = `${field} must be one of: ${rules.enum.join(', ')}`;
          continue;
        }
      }
      
      // Custom validation
      if (rules.validate && typeof rules.validate === 'function') {
        try {
          const result = rules.validate(value, req.body);
          if (result !== true) {
            errors[field] = result || `${field} is invalid`;
            continue;
          }
        } catch (error) {
          errors[field] = error.message || `${field} validation failed`;
          continue;
        }
      }
    }
    
    // If there are validation errors, throw a ValidationError
    if (Object.keys(errors).length > 0) {
      return next(new ValidationError('Validation failed', errors));
    }
    
    // Add validated data to request
    req.validatedData = validatedData;
    next();
  };
};

/**
 * Common validation schemas
 */
const schemas = {
  // User schemas
  createUser: {
    username: { 
      required: true, 
      type: 'string', 
      min: 3, 
      max: 50,
      pattern: '^[a-zA-Z0-9_]+$',
      patternMessage: 'Username can only contain letters, numbers, and underscores'
    },
    password: { 
      required: true, 
      type: 'string', 
      min: 8,
      pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$',
      patternMessage: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    },
    name: { required: true, type: 'string', min: 2, max: 100 },
    email: { 
      required: true, 
      type: 'string', 
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
      patternMessage: 'Please enter a valid email address'
    },
    role: { type: 'string', enum: ['admin', 'editor'] }
  },
  
  updateUser: {
    username: { 
      type: 'string', 
      min: 3, 
      max: 50,
      pattern: '^[a-zA-Z0-9_]+$',
      patternMessage: 'Username can only contain letters, numbers, and underscores'
    },
    name: { type: 'string', min: 2, max: 100 },
    email: { 
      type: 'string', 
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
      patternMessage: 'Please enter a valid email address'
    },
    role: { type: 'string', enum: ['admin', 'editor'] }
  },
  
  login: {
    username: { required: true, type: 'string' },
    password: { required: true, type: 'string' }
  },
  
  // Content schemas
  createContent: {
    pageName: { required: true, type: 'string', min: 1, max: 100 },
    content: { required: true },
    metaTitle: { type: 'string', max: 100 },
    metaDescription: { type: 'string', max: 200 }
  },
  
  updateContent: {
    content: { required: true },
    metaTitle: { type: 'string', max: 100 },
    metaDescription: { type: 'string', max: 200 }
  },
  
  // Inquiry schema
  createInquiry: {
    name: { required: true, type: 'string', min: 2, max: 100 },
    email: { 
      required: true, 
      type: 'string', 
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
      patternMessage: 'Please enter a valid email address'
    },
    phone: { type: 'string', max: 20 },
    subject: { type: 'string', max: 100 },
    message: { required: true, type: 'string', min: 10, max: 1000 }
  },
  
  // Reply to inquiry
  replyInquiry: {
    reply: { required: true, type: 'string', min: 10, max: 2000 }
  }
};

module.exports = {
  validateBody,
  schemas
};