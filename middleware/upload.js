const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { ValidationError } = require('./errorHandler');

// File upload configuration
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE) || 5242880; // 5MB default
const ALLOWED_FILE_TYPES = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif').split(',');

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '..', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  if (ALLOWED_FILE_TYPES.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError(`Invalid file type. Allowed types: ${ALLOWED_FILE_TYPES.join(', ')}`), false);
  }
};

// Create multer instance
const upload = multer({
  storage,
  limits: {
    fileSize: MAX_FILE_SIZE
  },
  fileFilter
});

/**
 * Single file upload middleware
 * @param {string} fieldName - Form field name for the file
 * @returns {Function} Express middleware
 */
const uploadSingleFile = (fieldName) => {
  return (req, res, next) => {
    upload.single(fieldName)(req, res, (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return next(new ValidationError(`File too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB`));
          }
          return next(new ValidationError(err.message));
        }
        return next(err);
      }
      next();
    });
  };
};

/**
 * Multiple files upload middleware
 * @param {string} fieldName - Form field name for the files
 * @param {number} maxCount - Maximum number of files
 * @returns {Function} Express middleware
 */
const uploadMultipleFiles = (fieldName, maxCount) => {
  return (req, res, next) => {
    upload.array(fieldName, maxCount)(req, res, (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return next(new ValidationError(`File too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB`));
          } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return next(new ValidationError(`Too many files. Maximum is ${maxCount}`));
          }
          return next(new ValidationError(err.message));
        }
        return next(err);
      }
      next();
    });
  };
};

/**
 * Process uploaded file and add file info to request
 * @param {Object} req - Express request object
 * @param {Object} file - Uploaded file object
 * @returns {Object} File info object
 */
const processUploadedFile = (req, file) => {
  if (!file) return null;
  
  const fileUrl = `/uploads/${file.filename}`;
  const fileType = file.mimetype.startsWith('image/') ? 'image' :
                  file.mimetype.startsWith('video/') ? 'video' : 'document';
  
  return {
    originalName: file.originalname,
    filename: file.filename,
    url: fileUrl,
    type: fileType,
    mimetype: file.mimetype,
    size: `${(file.size / 1024).toFixed(2)} KB`,
    path: file.path
  };
};

module.exports = {
  uploadSingleFile,
  uploadMultipleFiles,
  processUploadedFile,
  ALLOWED_FILE_TYPES,
  MAX_FILE_SIZE
};