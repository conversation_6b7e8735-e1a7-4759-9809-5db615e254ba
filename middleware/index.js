const { authenticateToken, requireAdmin } = require('./auth');
const { errorHandler, ValidationError, UnauthorizedError, ForbiddenError, NotFoundError } = require('./errorHandler');
const { validateBody, schemas } = require('./validation');
const { uploadSingleFile, uploadMultipleFiles, processUploadedFile } = require('./upload');

/**
 * Middleware for logging requests
 */
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log when the request completes
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`);
  });
  
  next();
};

/**
 * Middleware to handle CORS preflight requests
 */
const corsMiddleware = (req, res, next) => {
  res.header('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || 'http://localhost:3000');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  
  next();
};

/**
 * Middleware to check if database is available
 */
const checkDatabaseConnection = (pool) => {
  return async (req, res, next) => {
    if (!pool) {
      return res.status(500).json({ error: 'Database not available' });
    }
    
    try {
      // Test database connection
      const client = await pool.connect();
      await client.query('SELECT 1');
      client.release();
      next();
    } catch (error) {
      console.error('Database connection error:', error);
      return res.status(500).json({ error: 'Database connection failed' });
    }
  };
};

module.exports = {
  // Auth middleware
  authenticateToken,
  requireAdmin,
  
  // Error handling
  errorHandler,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  
  // Validation
  validateBody,
  schemas,
  
  // File uploads
  uploadSingleFile,
  uploadMultipleFiles,
  processUploadedFile,
  
  // Other middleware
  requestLogger,
  corsMiddleware,
  checkDatabaseConnection
};