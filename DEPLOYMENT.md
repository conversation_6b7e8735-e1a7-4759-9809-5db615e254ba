# Deployment Checklist

## Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Node.js v14+ installed
- [ ] PostgreSQL v12+ installed and running
- [ ] PM2 installed globally (`npm install -g pm2`)
- [ ] Git repository cloned
- [ ] All dependencies installed (`npm install`)

### 2. Environment Variables
Create `.env` file with:
```env
# Server
PORT=3000
NODE_ENV=production

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=lux_voyage
DB_USER=postgres
DB_PASSWORD=your_secure_password

# JWT
JWT_SECRET=your_very_secure_jwt_secret_key
JWT_EXPIRES_IN=24h

# CORS
CORS_ORIGIN=https://your-domain.com

# File Upload
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif

# Frontend
REACT_APP_API_URL=https://api.your-domain.com
REACT_APP_CDN_URL=https://cdn.your-domain.com
```

### 3. Database Setup
```sql
-- Create database
CREATE DATABASE lux_voyage;

-- Create user (if not exists)
CREATE USER lux_voyage_user WITH ENCRYPTED PASSWORD 'your_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE lux_voyage TO lux_voyage_user;
```

### 4. File Permissions
```bash
# Set proper permissions
chmod 755 uploads/
chmod 755 logs/
chmod 644 .env
```

## Deployment Steps

### 1. Build Application
```bash
npm run build:prod
```

### 2. Test Environment
```bash
npm run test:env
```

### 3. Test API Integration
```bash
npm run test:api
```

### 4. Deploy to Production
```bash
npm run deploy:prod
```

### 5. Verify Deployment
```bash
# Check PM2 status
pm2 status

# Check logs
pm2 logs

# Test endpoints
curl https://your-domain.com/api/health
```

## Post-Deployment Verification

### 1. Health Checks
- [ ] Server responds on port 3000
- [ ] Database connection successful
- [ ] File uploads working
- [ ] Authentication working
- [ ] All API endpoints responding

### 2. Security Verification
- [ ] HTTPS enforced
- [ ] CORS properly configured
- [ ] Rate limiting active
- [ ] JWT tokens working
- [ ] File upload restrictions active

### 3. Performance Checks
- [ ] Response times under 200ms
- [ ] Database queries optimized
- [ ] Static files served efficiently
- [ ] Memory usage stable

## Monitoring Setup

### 1. PM2 Monitoring
```bash
# Monitor processes
pm2 monit

# View logs
pm2 logs

# Restart if needed
pm2 restart all
```

### 2. Database Monitoring
```bash
# Check database size
psql -d lux_voyage -c "SELECT pg_size_pretty(pg_database_size('lux_voyage'));"

# Check active connections
psql -d lux_voyage -c "SELECT count(*) FROM pg_stat_activity;"
```

### 3. File System Monitoring
```bash
# Check disk usage
df -h

# Check uploads directory
du -sh uploads/
```

## Backup Strategy

### 1. Database Backups
```bash
# Manual backup
npm run db:backup

# Automated backup (add to crontab)
0 2 * * * cd /path/to/app && npm run db:backup
```

### 2. File Backups
```bash
# Backup uploads
tar -czf backup-uploads-$(date +%Y%m%d).tar.gz uploads/
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   lsof -i :3000
   kill -9 <PID>
   ```

2. **Database connection failed**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Restart PostgreSQL
   sudo systemctl restart postgresql
   ```

3. **Permission denied**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER /path/to/app
   chmod 755 uploads/
   ```

4. **PM2 process not starting**
   ```bash
   # Clear PM2 cache
   pm2 delete all
   pm2 start ecosystem.config.js
   ```

## Rollback Procedure

If deployment fails:
```bash
npm run deploy:rollback
```

## Support

For issues:
1. Check logs: `pm2 logs`
2. Check database: `npm run db:status`
3. Restart services: `pm2 restart all`
4. Contact support team 

## Additional Instructions

### Install PostgreSQL on Ubuntu/Debian
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
``` 