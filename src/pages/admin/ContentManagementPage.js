import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEdit, faSave, faUndo, faFileAlt, faEye } from '@fortawesome/free-solid-svg-icons';

const PageContainer = styled.div`
  padding: 20px;
`;

const PageHeader = styled.div`
  margin-bottom: 30px;

  h1 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
`;

const ContentTabs = styled.div`
  display: flex;
  border-bottom: 2px solid #f0f0f0;
  margin-bottom: 30px;
`;

const Tab = styled.button`
  padding: 12px 24px;
  border: none;
  background: ${props => props.active ? '#0099b8' : 'transparent'};
  color: ${props => props.active ? 'white' : '#666'};
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.active ? '#0099b8' : '#f5f5f5'};
  }
`;

const ContentSection = styled.div`
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
`;

const ActionButton = styled.button`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &.edit {
    background: #ff9800;
    color: white;
    &:hover { background: #f57c00; }
  }

  &.save {
    background: #4caf50;
    color: white;
    &:hover { background: #388e3c; }
  }

  &.cancel {
    background: #f44336;
    color: white;
    &:hover { background: #d32f2f; }
  }

  &.preview {
    background: #2196f3;
    color: white;
    &:hover { background: #1976d2; }
  }
`;

const FormGroup = styled.div`
  margin-bottom: 20px;

  label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  input, textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #0099b8;
    }
  }

  textarea {
    min-height: 120px;
    resize: vertical;
  }
`;

const ArrayEditor = styled.div`
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;

  .array-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;

    input {
      flex: 1;
      margin-bottom: 0;
    }

    button {
      padding: 6px 12px;
      background: #f44336;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  }

  .add-button {
    padding: 8px 16px;
    background: #4caf50;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 10px;
  }
`;

const LoadingSpinner = styled.div`
  text-align: center;
  padding: 40px;
  color: #666;
`;

const SuccessMessage = styled.div`
  background: #d4edda;
  color: #155724;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
`;

const ErrorMessage = styled.div`
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
`;

const ContentManagementPage = () => {
  const [activeTab, setActiveTab] = useState('about-us');
  const [content, setContent] = useState(null);
  const [editingSection, setEditingSection] = useState(null);
  const [editedContent, setEditedContent] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  useEffect(() => {
    fetchContent();
  }, [activeTab]);

  const fetchContent = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/page-content/${activeTab}`);
      
      if (response.ok) {
        const data = await response.json();
        setContent(data);
      } else {
        console.error('Failed to fetch content');
        setContent(null);
      }
    } catch (error) {
      console.error('Error fetching content:', error);
      setContent(null);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (section) => {
    setEditingSection(section);
    setEditedContent({ ...content.content[section] });
  };

  const handleSave = async (section) => {
    try {
      setSaving(true);
      
      const updatedContent = {
        ...content.content,
        [section]: editedContent
      };

      const response = await fetch(`/api/page-content/${activeTab}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          content: updatedContent,
          metaTitle: content.metaTitle,
          metaDescription: content.metaDescription
        })
      });

      if (response.ok) {
        const updatedData = await response.json();
        setContent(updatedData);
        setEditingSection(null);
        setMessage({ type: 'success', text: 'Content updated successfully!' });
        
        // Clear message after 3 seconds
        setTimeout(() => setMessage({ type: '', text: '' }), 3000);
      } else {
        throw new Error('Failed to update content');
      }
    } catch (error) {
      console.error('Error saving content:', error);
      setMessage({ type: 'error', text: 'Failed to save content. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingSection(null);
    setEditedContent({});
  };

  const handleArrayChange = (field, index, value) => {
    let parsedValue = value;
    // Attempt to get the original item's type from the 'content' state
    // This assumes 'content' state is structured similarly to 'editedContent'
    // and reflects the original data loaded from the server.
    if (content && content.content && content.content[activeTab] && content.content[activeTab][field] && content.content[activeTab][field][index] !== undefined) {
      const originalItem = content.content[activeTab][field][index];
      const originalType = typeof originalItem;

      if (originalType === 'number') {
        const num = parseFloat(value);
        if (!isNaN(num)) {
          parsedValue = num;
        }
        // If parsing fails, it remains a string, or you could add error handling
      } else if (originalType === 'boolean') {
        if (value.toLowerCase() === 'true') {
          parsedValue = true;
        } else if (value.toLowerCase() === 'false') {
          parsedValue = false;
        }
        // If not 'true' or 'false', it remains a string, or handle as error
      } else if (originalType === 'object') { // This could be an object or an array
        try {
          parsedValue = JSON.parse(value);
        } catch (e) {
          // If JSON parsing fails, it remains a string.
          // console.warn(`Failed to parse JSON for array item ${field}[${index}]:`, e);
        }
      }
      // If originalType is 'string', no conversion is needed, parsedValue is already value.
    } else {
      // Fallback for new items or if original content structure is different
      // Try to infer type for new items, or default to string
      try {
        parsedValue = JSON.parse(value);
      } catch (e) {
        const num = parseFloat(value);
        if (!isNaN(num)) {
          parsedValue = num;
        } else if (value.toLowerCase() === 'true') {
          parsedValue = true;
        } else if (value.toLowerCase() === 'false') {
          parsedValue = false;
        }
        // else it remains a string
      }
    }

    setEditedContent(prev => {
      const newArray = [...(prev[field] || [])];
      newArray[index] = parsedValue;
      return { ...prev, [field]: newArray };
    });
  };

  const handleArrayAdd = (field) => {
    const newArray = [...(editedContent[field] || []), ''];
    setEditedContent({ ...editedContent, [field]: newArray });
  };

  const handleArrayRemove = (field, index) => {
    const newArray = [...(editedContent[field] || [])];
    newArray.splice(index, 1);
    setEditedContent({ ...editedContent, [field]: newArray });
  };

  const renderEditForm = (section, data) => {
    if (editingSection !== section) {
      return (
        <div>
          <pre style={{ background: '#f5f5f5', padding: '15px', borderRadius: '6px', fontSize: '14px' }}>
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      );
    }

    return (
      <div>
        {Object.entries(editedContent).map(([key, value]) => (
          <FormGroup key={key}>
            <label>{key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}</label>
            {Array.isArray(value) ? (
              <ArrayEditor>
                {value.map((item, index) => (
                  <div key={index} className="array-item">
                    <input
                      type="text"
                      value={typeof item === 'string' ? item : JSON.stringify(item)}
                      onChange={(e) => handleArrayChange(key, index, e.target.value)}
                    />
                    <button onClick={() => handleArrayRemove(key, index)}>Remove</button>
                  </div>
                ))}
                <button className="add-button" onClick={() => handleArrayAdd(key)}>
                  Add Item
                </button>
              </ArrayEditor>
            ) : typeof value === 'string' ? (
              value.length > 100 ? (
                <textarea
                  value={value}
                  onChange={(e) => setEditedContent({ ...editedContent, [key]: e.target.value })}
                />
              ) : (
                <input
                  type="text"
                  value={value}
                  onChange={(e) => setEditedContent({ ...editedContent, [key]: e.target.value })}
                />
              )
            ) : (
              <textarea
                value={JSON.stringify(value, null, 2)}
                onChange={(e) => {
                  try {
                    const parsed = JSON.parse(e.target.value);
                    setEditedContent({ ...editedContent, [key]: parsed });
                  } catch (err) {
                    // Keep the raw text for now
                  }
                }}
              />
            )}
          </FormGroup>
        ))}
      </div>
    );
  };

  if (loading) {
    return <LoadingSpinner>Loading content...</LoadingSpinner>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1>Content Management</h1>
        <p>Edit and manage website page content</p>
      </PageHeader>

      {message.text && (
        message.type === 'success' ? (
          <SuccessMessage>{message.text}</SuccessMessage>
        ) : (
          <ErrorMessage>{message.text}</ErrorMessage>
        )
      )}

      <ContentTabs>
        <Tab active={activeTab === 'about-us'} onClick={() => setActiveTab('about-us')}>
          <FontAwesomeIcon icon={faFileAlt} /> About Us
        </Tab>
      </ContentTabs>

      {content && (
        <>
          {Object.entries(content.content).map(([section, data]) => (
            <ContentSection key={section}>
              <SectionHeader>
                <h3>{section.charAt(0).toUpperCase() + section.slice(1).replace(/([A-Z])/g, ' $1')}</h3>
                <ActionButtons>
                  {editingSection === section ? (
                    <>
                      <ActionButton 
                        className="save" 
                        onClick={() => handleSave(section)}
                        disabled={saving}
                      >
                        <FontAwesomeIcon icon={faSave} />
                        {saving ? 'Saving...' : 'Save'}
                      </ActionButton>
                      <ActionButton className="cancel" onClick={handleCancel}>
                        <FontAwesomeIcon icon={faUndo} />
                        Cancel
                      </ActionButton>
                    </>
                  ) : (
                    <>
                      <ActionButton className="edit" onClick={() => handleEdit(section)}>
                        <FontAwesomeIcon icon={faEdit} />
                        Edit
                      </ActionButton>
                      <ActionButton className="preview" onClick={() => window.open('/about', '_blank')}>
                        <FontAwesomeIcon icon={faEye} />
                        Preview
                      </ActionButton>
                    </>
                  )}
                </ActionButtons>
              </SectionHeader>
              {renderEditForm(section, data)}
            </ContentSection>
          ))}
        </>
      )}
    </PageContainer>
  );
};

export default ContentManagementPage;
